import { auth } from "./firebase"

/**
 * Stripe Client Service
 *
 * This service provides client-side functions for interacting with <PERSON><PERSON>.
 *
 * For testing, you can use these test card numbers:
 * - 4242 4242 4242 4242: Successful payment
 * - 4000 0000 0000 0002: Card declined
 * - 4000 0000 0000 0341: Subscription initially succeeds but fails on renewal
 *
 * See docs/stripe-testing.md for more test card numbers and scenarios.
 */

// Subscription plan IDs from environment variables
export const PLANS = {
  MONTHLY: process.env.NEXT_PUBLIC_STRIPE_PRICE_ID_MONTHLY!,
  YEARLY: process.env.NEXT_PUBLIC_STRIPE_PRICE_ID_YEARLY!,
}

// Subscription prices (for display purposes)
export const SUBSCRIPTION_PRICES = {
  MONTHLY: 7.99,
  YEARLY: 79.99,
}

// Subscription limits
export const SUBSCRIPTION_LIMITS = {
  FREE: {
    MAX_SQUADS: 1,
    MAX_TRIPS_PER_SQUAD: 2,
    MAX_DAILY_AI_REQUESTS: 10,
    MAX_WEEKLY_AI_REQUESTS: 50,
    HAS_TRIP_CHAT: false,
  },
  PRO: {
    MAX_SQUADS: 5,
    MAX_TRIPS_PER_SQUAD: 3,
    MAX_DAILY_AI_REQUESTS: Infinity,
    MAX_WEEKLY_AI_REQUESTS: Infinity,
    HAS_TRIP_CHAT: true,
  },
}

/**
 * Creates a Stripe checkout session for subscription
 *
 * @param priceId The Stripe price ID for the subscription plan
 * @throws Error If the user is not authenticated or if the checkout session creation fails
 */
export const createCheckoutSession = async (
  priceId: string
): Promise<{ url: string; sessionId: string }> => {
  try {
    const user = auth.currentUser
    if (!user) {
      throw new Error("User not authenticated")
    }

    const token = await user.getIdToken()
    const response = await fetch("/api/stripe/checkout", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({ priceId }),
    })

    if (!response.ok) {
      const errorData = await response.json()
      // Pass through the specific error message from the API
      const errorMessage =
        errorData.message || errorData.error || "Failed to create checkout session"
      throw new Error(errorMessage)
    }

    const data = await response.json()
    return data
  } catch (error) {
    console.error("Error creating checkout session:", error)
    throw error
  }
}

/**
 * Creates a Stripe customer portal session for managing subscriptions
 *
 * @throws Error If the user is not authenticated or doesn't have a Stripe customer ID
 */
export const createCustomerPortalSession = async (): Promise<{ url: string }> => {
  try {
    const user = auth.currentUser
    if (!user) {
      throw new Error("User not authenticated")
    }

    const token = await user.getIdToken()
    const response = await fetch("/api/stripe/portal", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    })

    if (!response.ok) {
      const errorData = await response.json()
      // Pass through the specific error message from the API
      const errorMessage =
        errorData.message || errorData.error || "Failed to create customer portal session"
      throw new Error(errorMessage)
    }

    const data = await response.json()
    return data
  } catch (error) {
    console.error("Error creating customer portal session:", error)
    // Rethrow the error to be handled by the component
    throw error
  }
}

// Format a date to a readable string
export const formatDate = (timestamp: any): string => {
  if (!timestamp) return "N/A"

  const dateFormat = {
    year: "numeric",
    month: "long",
    day: "numeric",
  } as Intl.DateTimeFormatOptions

  try {
    // For development debugging
    if (process.env.NODE_ENV === "development") {
      console.log("Formatting date from:", typeof timestamp, timestamp)
    }

    // Handle Firebase Timestamp
    if (timestamp && typeof timestamp.toDate === "function") {
      return new Date(timestamp.toDate()).toLocaleDateString("en-US", dateFormat)
    }

    // Handle seconds timestamp (from Firestore server timestamp)
    if (typeof timestamp === "number") {
      // Check if this is seconds (Firestore) or milliseconds (JS Date)
      // If the number is less than 2000000000, it's likely seconds (before 2033)
      const milliseconds = timestamp < 2000000000 ? timestamp * 1000 : timestamp
      return new Date(milliseconds).toLocaleDateString("en-US", dateFormat)
    }

    // Handle string timestamps that might be numbers
    if (typeof timestamp === "string" && !isNaN(Number(timestamp))) {
      const numericTimestamp = Number(timestamp)
      // Check if this is seconds (Firestore) or milliseconds (JS Date)
      const milliseconds =
        numericTimestamp < 2000000000 ? numericTimestamp * 1000 : numericTimestamp
      return new Date(milliseconds).toLocaleDateString("en-US", dateFormat)
    }

    // Handle object with seconds and nanoseconds (Firestore timestamp format)
    if (typeof timestamp === "object" && timestamp !== null && "seconds" in timestamp) {
      const seconds = (timestamp as any).seconds
      if (typeof seconds === "number") {
        const milliseconds = seconds * 1000
        return new Date(milliseconds).toLocaleDateString("en-US", dateFormat)
      }
    }

    // Handle Date object or other timestamp formats
    return new Date(timestamp).toLocaleDateString("en-US", dateFormat)
  } catch (error) {
    if (process.env.NODE_ENV === "development") {
      console.error("Error formatting date:", error, typeof timestamp, timestamp)
    }
    return "Invalid date"
  }
}
