"use client"

import { useEffect, useState, useR<PERSON>, useMem<PERSON> } from "react"
import { use<PERSON><PERSON><PERSON>, use<PERSON><PERSON><PERSON>, useSearchParams } from "next/navigation"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/components/ui/use-toast"
import { useAuthStatus } from "@/lib/domains/auth/auth.hooks"
import { AppHeader } from "@/components/app-header"
import { AppSidebar } from "@/components/app-sidebar"
import { PageLoading } from "@/components/page-loading"

// Import domain hooks
import { useRealtimeTrip, useIsTripLeader } from "@/lib/domains/trip/trip.hooks"
import { useRealtimeTripTasks } from "@/lib/domains/task/task.realtime.hooks"
import {
  useRealtimeUserTripStatus,
  useRealtimeTripAttendeesWithDetails,
} from "@/lib/domains/user-trip/user-trip.realtime.hooks"
import { useRealtimeSquad, useRealtimeSquadMembers } from "@/lib/domains/squad/squad.realtime.hooks"

// Import our components
import { TripHeader } from "./components/shared/header"
import { TripOverviewTab } from "./components/overview/overview-tab"
import { TasksTab } from "./components/tasks/tasks-tab"
import { TripItineraryTab } from "./components/itinerary/itinerary-tab"
import { TripExpensesTab } from "./components/expenses/expenses-tab"
import { AttendeesTab } from "./components/attendees/attendees-tab"
import { SettingsTab } from "./components/settings/settings-tab"
import { ChatTab } from "./components/chat/chat-tab"
import { AttendanceToggleButton } from "./components/shared/attendance-toggle-button"
import { FloatingAIButton } from "@/components/floating-ai-button"

export default function TripPage() {
  const params = useParams()
  const router = useRouter()
  const searchParams = useSearchParams()
  const { toast } = useToast()
  const { user, loading: authLoading } = useAuthStatus()
  const tripId = params.id as string

  // Get tab from URL query parameter or default to "overview"
  const tabParam = searchParams.get("tab")
  const validTabs = ["overview", "tasks", "itinerary", "expenses", "attendees", "chat", "settings"]

  // State for active tab when user is attending
  const [activeTab, setActiveTab] = useState(
    validTabs.includes(tabParam || "") ? tabParam : "overview"
  )
  const tabsListRef = useRef<HTMLDivElement>(null)
  const [isTripOngoing, setIsTripOngoing] = useState<boolean>(false)

  // Function to handle tab changes with URL updates
  const handleTabChange = (value: string) => {
    setActiveTab(value)
    // Update URL without refreshing the page
    const url = new URL(window.location.href)
    url.searchParams.set("tab", value)
    window.history.pushState({}, "", url.toString())
  }

  // Get trip data with real-time updates
  const { trip, loading: tripLoading, error: tripError } = useRealtimeTrip(tripId)

  // Check if user is trip leader
  const { isLeader, loading: leaderCheckLoading } = useIsTripLeader(tripId)

  // Get trip tasks with real-time updates (only if on tasks tab)
  const {
    tasks,
    loading: tasksLoading,
    error: tasksError,
  } = useRealtimeTripTasks(activeTab === "tasks" ? tripId : "")

  // Get user's trip status with real-time updates
  const { status, loading: statusLoading, error: statusError } = useRealtimeUserTripStatus(tripId)

  // Get trip attendees with user details in a single real-time update
  const {
    attendeesWithDetails,
    loading: attendeesLoading,
    error: attendeesError,
  } = useRealtimeTripAttendeesWithDetails(tripId)

  // Extract the separate arrays for backward compatibility with components (memoized to prevent re-renders)
  const attendees = useMemo(
    () => attendeesWithDetails.map((a) => ({ ...a })),
    [attendeesWithDetails]
  )
  const attendeesDetails = useMemo(
    () => attendeesWithDetails.map((a) => a.user),
    [attendeesWithDetails]
  )

  // Create chat-compatible attendees mapping
  const chatAttendees = useMemo(
    () =>
      attendeesWithDetails
        .filter((a) => a.user?.uid) // Filter out any attendees without valid UIDs
        .map((a) => ({
          id: a.user.uid, // Map uid to id for chat compatibility
          displayName: a.user.displayName || a.user.email || "Unknown User",
          email: a.user.email,
          photoURL: a.user.photoURL,
        })),
    [attendeesWithDetails]
  )

  // Get squad data with real-time updates (only when needed)
  const { squad, loading: squadLoading, error: squadError } = useRealtimeSquad(trip?.squadId || "")

  // Get squad members with real-time updates (only when needed for attendees tab)
  const { members: squadMembers } = useRealtimeSquadMembers(
    activeTab === "attendees" && trip?.squadId ? trip.squadId : ""
  )

  // Extract squad member user IDs for the attendees tab
  const squadMemberIds = useMemo(
    () => squadMembers?.map((member) => member.userId) || [],
    [squadMembers]
  )

  // Check if user has restricted access (for active trips, only going users get full access)
  const hasRestrictedAccess = trip?.status === "active" && (!status || status !== "going")

  // Available tabs based on user access
  const availableTabs = hasRestrictedAccess
    ? ["overview"] // Only overview for non-going users on active trips
    : ["overview", "tasks", "itinerary", "expenses", "attendees", "chat"] // Full access for others

  // Effect to update active tab when URL changes
  useEffect(() => {
    const tab = searchParams.get("tab")
    if (tab && validTabs.includes(tab) && tab !== activeTab) {
      // Prevent non-leaders from accessing settings tab
      if (tab === "settings" && !isLeader) {
        setActiveTab("overview")
        // Update URL to remove invalid tab
        const url = new URL(window.location.href)
        url.searchParams.set("tab", "overview")
        window.history.replaceState({}, "", url.toString())
      }
      // Prevent restricted users from accessing non-overview tabs
      else if (hasRestrictedAccess && !availableTabs.includes(tab)) {
        setActiveTab("overview")
        // Update URL to remove invalid tab
        const url = new URL(window.location.href)
        url.searchParams.set("tab", "overview")
        window.history.replaceState({}, "", url.toString())
      } else {
        setActiveTab(tab)
      }
    }
  }, [searchParams, validTabs, activeTab, isLeader, hasRestrictedAccess, availableTabs])

  // Effect to scroll active tab into view on mobile
  useEffect(() => {
    if (tabsListRef.current && activeTab) {
      setTimeout(() => {
        const tabElement = tabsListRef.current?.querySelector(`[data-value="${activeTab}"]`)
        if (tabElement && tabsListRef.current) {
          // Scroll the tab into view with some padding
          const container = tabsListRef.current
          const scrollLeft =
            tabElement.getBoundingClientRect().left -
            container.getBoundingClientRect().left +
            container.scrollLeft -
            16 // Add some padding

          container.scrollTo({
            left: scrollLeft,
            behavior: "smooth",
          })
        }
      }, 100) // Small delay to ensure the DOM is updated
    }
  }, [activeTab])

  // Check if trip is ongoing
  useEffect(() => {
    if (trip?.startDate && trip?.endDate) {
      const now = new Date()
      const tripStartDate = trip.startDate.toDate()
      const tripEndDate = trip.endDate.toDate()
      setIsTripOngoing(now >= tripStartDate && now <= tripEndDate)
    }
  }, [trip])

  // Handle trip not found or error
  useEffect(() => {
    if (!tripLoading && tripError) {
      toast({
        title: "Error",
        description: "Trip not found or could not be loaded",
        variant: "destructive",
      })
      router.push("/trips")
    }
  }, [tripLoading, tripError, toast, router])

  // Determine if user is attending the trip
  const userAttendingTrip = status === "going"

  // Calculate loading state
  const loading = authLoading || tripLoading
  // Handle floating AI button click
  const handleFloatingAIClick = () => {
    if (activeTab === "tasks") {
      // Scroll to AI Task Suggestions section
      const taskSuggestionsElement = document.getElementById("ai-task-suggestions")
      if (taskSuggestionsElement) {
        taskSuggestionsElement.scrollIntoView({ behavior: "smooth", block: "start" })
      }
    } else if (activeTab === "itinerary") {
      // Scroll to AI Activities Suggestions section
      const itinerarySuggestionsElement = document.getElementById("ai-activities-suggestions")
      if (itinerarySuggestionsElement) {
        itinerarySuggestionsElement.scrollIntoView({ behavior: "smooth", block: "start" })
      }
    }
  }

  // Show floating button only on tasks and itinerary tabs when user is attending
  const showFloatingButton =
    userAttendingTrip && (activeTab === "tasks" || activeTab === "itinerary")

  if (loading || !trip) {
    return <PageLoading message="Loading trip details..." />
  }

  // Get pending tasks for overview
  const pendingTasks = tasks?.filter((task) => !task.completed).slice(0, 3) || []
  const squadName = squad?.name || "Squad"

  return (
    <div className="min-h-screen flex flex-col overflow-x-hidden">
      <AppHeader />

      <div className="flex-1 flex">
        <AppSidebar />

        <main className="flex-1 p-4 md:p-6 overflow-y-auto overflow-x-hidden">
          <TripHeader trip={trip} attendees={attendees} squadName={squadName} />

          {!userAttendingTrip ? (
            // Only show overview tab if user is not attending
            <div className="space-y-4 md:space-y-6">
              <div className="space-y-3 md:space-y-4 mb-3 md:mb-4">
                <div className="flex justify-between items-center">
                  <h2 className="text-lg md:text-xl font-semibold">Trip Overview</h2>
                </div>
                {!isTripOngoing && (
                  <div className="bg-muted/30 p-2 px-3 rounded-lg border shadow-sm">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="text-xs font-medium text-muted-foreground">
                        Your attendance:
                      </span>
                    </div>
                    <AttendanceToggleButton
                      tripId={tripId}
                      status={status}
                      tripStatus={trip.status}
                    />
                  </div>
                )}
              </div>
              <TripOverviewTab
                trip={trip}
                attendees={attendees}
                attendeesDetails={attendeesDetails}
                pendingTasks={pendingTasks}
                isLeader={isLeader}
                squadName={squadName}
                userAttendingTrip={userAttendingTrip}
                isActive={true}
              />
            </div>
          ) : (
            // Show all tabs if user is attending
            <Tabs
              defaultValue="overview"
              className="space-y-4 w-full"
              onValueChange={handleTabChange}
              value={activeTab || "overview"}
            >
              <div className="flex flex-col space-y-4">
                {!isTripOngoing && (
                  <div className="bg-muted/30 p-2 px-3 rounded-lg border shadow-sm md:hidden w-full">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="text-xs font-medium text-muted-foreground">
                        Your attendance:
                      </span>
                    </div>
                    <AttendanceToggleButton
                      tripId={tripId}
                      status={status}
                      tripStatus={trip.status}
                    />
                  </div>
                )}
                <div className="flex justify-between items-center w-full">
                  <div className="w-full overflow-x-auto pb-2 no-scrollbar">
                    <TabsList
                      className={`inline-flex w-max md:w-full md:grid ${
                        isLeader ? "md:grid-cols-7" : "md:grid-cols-6"
                      }`}
                      ref={tabsListRef}
                      style={{ paddingLeft: 0, paddingRight: 0 }}
                    >
                      <TabsTrigger
                        value="overview"
                        className="min-w-[100px] md:flex-1 whitespace-nowrap"
                      >
                        Overview
                      </TabsTrigger>
                      {availableTabs.includes("tasks") && (
                        <TabsTrigger
                          value="tasks"
                          className="min-w-[80px] md:flex-1 whitespace-nowrap"
                        >
                          Tasks
                        </TabsTrigger>
                      )}
                      {availableTabs.includes("itinerary") && (
                        <TabsTrigger
                          value="itinerary"
                          className="min-w-[100px] md:flex-1 whitespace-nowrap"
                        >
                          Itinerary
                        </TabsTrigger>
                      )}
                      {availableTabs.includes("chat") && (
                        <TabsTrigger
                          value="chat"
                          className="min-w-[80px] md:flex-1 whitespace-nowrap"
                        >
                          Chat
                        </TabsTrigger>
                      )}
                      {availableTabs.includes("expenses") && (
                        <TabsTrigger
                          value="expenses"
                          className="min-w-[100px] md:flex-1 whitespace-nowrap"
                        >
                          Expenses
                        </TabsTrigger>
                      )}
                      {availableTabs.includes("attendees") && (
                        <TabsTrigger
                          value="attendees"
                          className="min-w-[100px] md:flex-1 whitespace-nowrap"
                        >
                          Attendees
                        </TabsTrigger>
                      )}
                      
                      {isLeader && (
                        <TabsTrigger
                          value="settings"
                          className="min-w-[100px] md:flex-1 whitespace-nowrap"
                        >
                          Settings
                        </TabsTrigger>
                      )}
                    </TabsList>
                  </div>
                  {!isTripOngoing && (
                    <div className="bg-muted/30 p-2 px-3 rounded-lg border shadow-sm hidden md:block ml-4 shrink-0">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="text-xs font-medium text-muted-foreground">
                          Your attendance:
                        </span>
                      </div>
                      <AttendanceToggleButton
                        tripId={tripId}
                        status={status}
                        tripStatus={trip.status}
                      />
                    </div>
                  )}
                </div>
              </div>

              <TabsContent
                value="overview"
                className="space-y-6 w-full max-w-full overflow-x-hidden"
              >
                {hasRestrictedAccess && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                    <div className="flex items-center gap-2 text-blue-800">
                      <span className="text-sm font-medium">ℹ️ Limited Access</span>
                    </div>
                    <p className="text-blue-700 text-sm mt-1">
                      This trip is currently active. Since you're not marked as "going", you can
                      only view the trip overview. To access all trip features, you would have
                      needed to confirm your attendance before the trip started.
                    </p>
                  </div>
                )}
                <TripOverviewTab
                  trip={trip}
                  attendees={attendees}
                  attendeesDetails={attendeesDetails}
                  pendingTasks={pendingTasks}
                  isLeader={isLeader}
                  squadName={squadName}
                  userAttendingTrip={userAttendingTrip}
                  isActive={activeTab === "overview"}
                />
              </TabsContent>

              {availableTabs.includes("tasks") && (
                <TabsContent
                  value="tasks"
                  className="space-y-4 w-full max-w-full overflow-x-hidden"
                >
                  <TasksTab
                    trip={trip}
                    tasks={tasks}
                    currentUserId={user?.uid || ""}
                    attendeesWithDetails={attendeesWithDetails}
                    isActive={activeTab === "tasks"}
                  />
                </TabsContent>
              )}

              {availableTabs.includes("itinerary") && (
                <TabsContent
                  value="itinerary"
                  className="space-y-4 w-full max-w-full overflow-x-hidden"
                >
                  <TripItineraryTab trip={trip} isActive={activeTab === "itinerary"} />
                </TabsContent>
              )}

              {availableTabs.includes("expenses") && (
                <TabsContent
                  value="expenses"
                  className="space-y-4 w-full max-w-full overflow-x-hidden"
                >
                  <TripExpensesTab trip={trip} attendees={attendees} />
                </TabsContent>
              )}

              {availableTabs.includes("attendees") && (
                <TabsContent
                  value="attendees"
                  className="space-y-4 w-full max-w-full overflow-x-hidden"
                >
                  <AttendeesTab
                    trip={trip}
                    attendees={attendeesWithDetails}
                    squadMembers={squadMemberIds}
                    currentUserId={user?.uid || ""}
                    isActive={activeTab === "attendees"}
                  />
                </TabsContent>
              )}

              {availableTabs.includes("chat") && (
                <TabsContent value="chat" className="space-y-4 w-full max-w-full overflow-x-hidden">
                  <ChatTab trip={trip} attendees={chatAttendees} isActive={activeTab === "chat"} />
                </TabsContent>
              )}

              {isLeader && (
                <TabsContent
                  value="settings"
                  className="space-y-4 w-full max-w-full overflow-x-hidden"
                >
                  <SettingsTab trip={trip} />
                </TabsContent>
              )}
            </Tabs>
          )}
        </main>
      </div>

      {/* Floating AI Suggestions Button - positioned relative to viewport */}
      {showFloatingButton && <FloatingAIButton onClick={handleFloatingAIClick} />}
    </div>
  )
}
